"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/redis */ \"(middleware)/./src/lib/redis.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jwt-decode */ \"(middleware)/./node_modules/jwt-decode/build/esm/index.js\");\n\n\n\n\nasync function middleware(request) {\n    const isRedisPath = request.nextUrl.pathname === \"/api/status/redis\";\n    // Special path to check Redis status (useful for debugging)\n    if (isRedisPath) {\n        try {\n            const redisAvailable = (0,_lib_redis__WEBPACK_IMPORTED_MODULE_1__.isRedisAvailable)();\n            if (!redisAvailable) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: \"not_configured\",\n                    message: \"Redis is not configured. Check your environment variables.\",\n                    timestamp: new Date().toISOString()\n                }, {\n                    status: 200\n                });\n            }\n            // Test Redis connection with a simple operation\n            const testKey = \"middleware:test\";\n            const testValue = Date.now().toString();\n            await _lib_redis__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(testKey, testValue);\n            const retrieved = await _lib_redis__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(testKey);\n            if (retrieved === testValue) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: \"ok\",\n                    message: \"Redis is configured and working properly\",\n                    timestamp: new Date().toISOString()\n                }, {\n                    status: 200\n                });\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: \"error\",\n                    message: \"Redis is configured but not working properly\",\n                    timestamp: new Date().toISOString()\n                }, {\n                    status: 500\n                });\n            }\n        } catch (error) {\n            console.error(\"Redis check error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: \"error\",\n                message: \"Error checking Redis: \" + (error instanceof Error ? error.message : String(error)),\n                timestamp: new Date().toISOString()\n            }, {\n                status: 500\n            });\n        }\n    }\n    // Authentication check for protected routes\n    const isProtectedRoute = request.nextUrl.pathname.startsWith(\"/account\") || request.nextUrl.pathname.startsWith(\"/checkout\");\n    // Auth routes that should redirect to homepage if already logged in\n    const isAuthRoute = request.nextUrl.pathname.startsWith(\"/sign-in\") || request.nextUrl.pathname.startsWith(\"/sign-up\");\n    // Get auth token from cookies\n    const authToken = request.cookies.get(\"woo_auth_token\")?.value;\n    // Debug logging for protected routes\n    if (isProtectedRoute) {\n        console.log(\"Middleware: Protected route accessed:\", request.nextUrl.pathname);\n        console.log(\"Middleware: Auth token present:\", !!authToken);\n        console.log(\"Middleware: All cookies:\", request.cookies.getAll().map((c)=>c.name));\n        if (authToken) {\n            console.log(\"Middleware: Token length:\", authToken.length);\n            console.log(\"Middleware: Token starts with:\", authToken.substring(0, 20) + \"...\");\n        }\n    }\n    // Check if user is authenticated\n    let isAuthenticated = false;\n    if (authToken) {\n        try {\n            // Verify token expiration\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_2__.jwtDecode)(authToken);\n            const currentTime = Date.now() / 1000;\n            if (decoded.exp > currentTime) {\n                isAuthenticated = true;\n                if (isProtectedRoute) {\n                    console.log(\"Middleware: User authenticated, token valid\");\n                }\n            } else {\n                if (isProtectedRoute) {\n                    console.log(\"Middleware: Token expired\");\n                }\n            }\n        } catch (e) {\n            console.error(\"Error decoding token in middleware:\", e);\n        // Token is invalid, so user is not authenticated\n        }\n    }\n    // Redirect unauthenticated users away from protected routes\n    if (isProtectedRoute && !isAuthenticated) {\n        const signInUrl = new URL(\"/sign-in\", request.url);\n        // Add the original URL as a query parameter to redirect back after login\n        signInUrl.searchParams.set(\"redirect\", request.nextUrl.pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Redirect authenticated users away from auth pages to the homepage\n    if (isAuthRoute && isAuthenticated) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/\", request.url));\n    }\n    // Continue for all other requests\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n// Run middleware on the specified paths\nconst config = {\n    matcher: [\n        \"/api/status/redis\",\n        \"/account/:path*\",\n        \"/checkout/:path*\",\n        \"/sign-in\",\n        \"/sign-up\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});